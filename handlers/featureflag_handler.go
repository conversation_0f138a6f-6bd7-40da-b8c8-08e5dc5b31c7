package handlers

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type FeatureFlagHandler struct {
	Handler

	service services.FeatureFlagServiceInterface
}

func NewFeatureFlagHandler(featureFlagService services.FeatureFlagServiceInterface, logger *zap.Logger) *FeatureFlagHandler {
	return &FeatureFlagHandler{
		Handler: Handler{logger: logger},
		service: featureFlagService,
	}
}

func (h *FeatureFlagHandler) Get(w http.ResponseWriter, r *http.Request) {
	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	version := r.Header.Get("X-App-Version")
	if version == "" {
		h.logger.Error("missing X-App-Version header")
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	ctx := r.Context()
	featureFlag, err := h.service.GetBy(ctx, platform, version)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	if featureFlag == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, featureFlag, http.StatusOK)
	}
}
