package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/models"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupFeatureFlagHandler(t *testing.T) (*FeatureFlagHandler, *mocks.MockFeatureFlagServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockFeatureFlagServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewFeatureFlagHandler(mockService, logger)
	return handler, mockService, ctrl
}

func TestNewFeatureFlagHandler(t *testing.T) {
	t.Run("Creates feature flag handler correctly", func(t *testing.T) {
		handler, _, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.logger)
	})
}

func TestFeatureFlagHandler_Get(t *testing.T) {
	t.Run("Success - feature flag found with iOS platform", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000001"),
			Platform: "ios",
			Version:  "1.0.0",
			Config:   `{"feature1": true, "feature2": false}`,
		}

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformIOS, "1.0.0").
			Return(expectedFeatureFlag, nil)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.FeatureFlag
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, expectedFeatureFlag.ID, response.ID)
		assert.Equal(t, expectedFeatureFlag.Platform, response.Platform)
		assert.Equal(t, expectedFeatureFlag.Version, response.Version)
		assert.Equal(t, expectedFeatureFlag.Config, response.Config)
	})

	t.Run("Success - feature flag found without platform header (defaults to iOS)", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000001"),
			Platform: "ios",
			Version:  "1.0.0",
			Config:   `{"feature1": true, "feature2": false}`,
		}

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformIOS, "1.0.0").
			Return(expectedFeatureFlag, nil)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		req = AddPlatformToContext(req, models.PlatformIOS)

		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.FeatureFlag
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, expectedFeatureFlag.ID, response.ID)
		assert.Equal(t, expectedFeatureFlag.Platform, response.Platform)
		assert.Equal(t, expectedFeatureFlag.Version, response.Version)
		assert.Equal(t, expectedFeatureFlag.Config, response.Config)
	})

	t.Run("RawError - feature flag not found", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformIOS, "1.0.0").
			Return(nil, nil)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		req = AddPlatformToContext(req, models.PlatformIOS)

		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformIOS, "1.0.0").
			Return(nil, pkg.ErrInternal)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		req = AddPlatformToContext(req, models.PlatformIOS)

		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing X-App-Version header", func(t *testing.T) {
		handler, _, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - empty X-App-Version header", func(t *testing.T) {
		handler, _, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "")
		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Success - different version format", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		version := "v1.2.3-beta"
		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000001"),
			Platform: "ios",
			Version:  version,
			Config:   `{"feature1": true}`,
		}

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformIOS, version).
			Return(expectedFeatureFlag, nil)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", version)
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.FeatureFlag
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, version, response.Version)
	})

	t.Run("Success - Android platform", func(t *testing.T) {
		handler, mockService, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000002"),
			Platform: "android",
			Version:  "1.0.0",
			Config:   `{"androidFeature": true}`,
		}

		mockService.EXPECT().
			GetBy(gomock.Any(), models.PlatformAndroid, "1.0.0").
			Return(expectedFeatureFlag, nil)

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		req.Header.Set("X-App-Platform", "android")
		req = AddPlatformToContext(req, models.PlatformAndroid)

		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.FeatureFlag
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, expectedFeatureFlag.Platform, response.Platform)
		assert.Equal(t, "android", response.Platform)
	})

	t.Run("RawError - missing platform context", func(t *testing.T) {
		handler, _, ctrl := setupFeatureFlagHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/feature-flags", nil)
		req.Header.Set("X-App-Version", "1.0.0")
		w := httptest.NewRecorder()

		handler.Get(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
