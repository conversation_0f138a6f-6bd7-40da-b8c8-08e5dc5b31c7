package handlers

import (
	"net"
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/middlewares"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/appio-so/go-clientip"
	"github.com/go-chi/chi/v5"
	"go.uber.org/zap"
)

type Handler struct {
	logger *zap.Logger
}

func (h *Handler) GetPlatform(w http.ResponseWriter, r *http.Request) (models.Platform, bool) {
	ctx := r.Context()
	platform, ok := middlewares.GetPlatformFromContext(ctx)
	if !ok {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return "", false
	}
	return platform, true
}

func (h *Handler) GetServiceID(w http.ResponseWriter, r *http.Request) (*appioid.ID, bool) {
	ctx := r.Context()
	svcID, ok := middlewares.GetServiceIDFromContext(ctx)
	if !ok {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return nil, false
	}
	return svcID, true
}

func (h *Handler) GetDeviceID(w http.ResponseWriter, r *http.Request) (*appioid.ID, bool) {
	ctx := r.Context()
	dvcID, ok := middlewares.GetDeviceIDFromContext(ctx)
	if !ok {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return nil, false
	}
	return dvcID, ok
}

func (h *Handler) GetOrganizationID(w http.ResponseWriter, r *http.Request) (*appioid.ID, bool) {
	ctx := r.Context()
	orgID, ok := middlewares.GetOrganizationIDFromContext(ctx)
	if !ok {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return nil, false
	}
	return orgID, ok
}

func (h *Handler) GetUrlID(w http.ResponseWriter, r *http.Request) (*appioid.ID, bool) {
	ID, err := appioid.Parse(chi.URLParam(r, "id"))
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return nil, false
	}
	return ID, true
}

func (h *Handler) GetQueryID(r *http.Request, key string) (*appioid.ID, error) {
	id := r.URL.Query().Get(key)
	if id == "" {
		return nil, nil
	}
	return appioid.Parse(id)
}

func (h *Handler) ResponseIDsFrom(ids []appioid.ID) []models.ResponseID {
	response := make([]models.ResponseID, len(ids))
	for i, id := range ids {
		response[i] = models.ResponseID{ID: &id}
	}
	return response
}

func (h *Handler) GetIP(r *http.Request) net.IP {
	ip := clientip.FromRequest(r)
	if nil == ip {
		h.logger.Debug("null IP address, using localhost instead")
		ip = net.ParseIP("127.0.0.1")
	}
	return ip
}
