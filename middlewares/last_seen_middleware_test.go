package middlewares

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestLastSeenAtMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	t.Run("Updates last_seen_at when device ID exists in context", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		// Expect UpdateLastSeenAt to be called
		mockRepo.EXPECT().
			UpdateLastSeenAt(gomock.Any(), dvcID).
			Return(nil)

		// Handler that checks if the request continues
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := LastSeenAtMiddleware(mockRepo, logger)
		handler := middleware(nextHandler)

		// Create request with device ID in context
		req := httptest.NewRequest("GET", "/test", nil)
		ctx := context.WithValue(req.Context(), DvcIDKey{}, dvcID)
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Continues without error when device ID does not exist in context", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)

		// No expectations on mockRepo since no device ID in context

		// Handler that checks if the request continues
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := LastSeenAtMiddleware(mockRepo, logger)
		handler := middleware(nextHandler)

		// Create request without device ID in context
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Continues when UpdateLastSeenAt fails (fire-and-forget)", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		// Expect UpdateLastSeenAt to be called and return an error
		mockRepo.EXPECT().
			UpdateLastSeenAt(gomock.Any(), dvcID).
			Return(assert.AnError)

		// Handler that checks if the request continues despite the error
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := LastSeenAtMiddleware(mockRepo, logger)
		handler := middleware(nextHandler)

		// Create request with device ID in context
		req := httptest.NewRequest("GET", "/test", nil)
		ctx := context.WithValue(req.Context(), DvcIDKey{}, dvcID)
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should still return OK despite the UpdateLastSeenAt error
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Handles nil device ID gracefully", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)

		// No expectations on mockRepo since device ID is nil

		// Handler that checks if the request continues
		nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := LastSeenAtMiddleware(mockRepo, logger)
		handler := middleware(nextHandler)

		// Create request with nil device ID in context
		req := httptest.NewRequest("GET", "/test", nil)
		ctx := context.WithValue(req.Context(), DvcIDKey{}, (*appioid.ID)(nil))
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})
}
