package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"context"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
	"net/http"
)

// Parse ID from header if exists
func HeaderToContextIDMiddleware(header string, ctxKey any, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			headerID := r.Header.Get(header)
			if headerID != "" {
				id, err := appioid.Parse(headerID)
				if err != nil {
					logger.Info("invalid ID in header", zap.String("header", header), zap.Error(err))
					helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
					return
				}

				ctx = context.WithValue(ctx, ctxKey, id)
			}
			next.ServeHTTP(w, r.With<PERSON>onte<PERSON>(ctx))
		})
	}
}
