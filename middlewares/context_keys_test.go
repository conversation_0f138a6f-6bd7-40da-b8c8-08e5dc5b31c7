package middlewares

import (
	"api.appio.so/pkg/roles"
	"context"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetServiceIDFromContext(t *testing.T) {
	t.Run("Success - service ID found in context", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		result, ok := GetServiceIDFromContext(ctx)

		assert.True(t, ok)
		assert.NotNil(t, result)
		assert.Equal(t, svcID, result)
	})

	t.Run("RawError - service ID not found in context", func(t *testing.T) {
		ctx := context.Background()

		result, ok := GetServiceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - wrong type in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), SvcIDKey{}, "invalid-type")

		result, ok := GetServiceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - nil value in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), SvcIDKey{}, nil)

		result, ok := GetServiceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - different key type", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), "wrong-key", svcID)

		result, ok := GetServiceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})
}

func TestGetDeviceIDFromContext(t *testing.T) {
	t.Run("Success - device ID found in context", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), DvcIDKey{}, dvcID)

		result, ok := GetDeviceIDFromContext(ctx)

		assert.True(t, ok)
		assert.NotNil(t, result)
		assert.Equal(t, dvcID, result)
	})

	t.Run("RawError - device ID not found in context", func(t *testing.T) {
		ctx := context.Background()

		result, ok := GetDeviceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - wrong type in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), DvcIDKey{}, "invalid-type")

		result, ok := GetDeviceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - nil value in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), DvcIDKey{}, nil)

		result, ok := GetDeviceIDFromContext(ctx)

		assert.False(t, ok)
		assert.Nil(t, result)
	})

	t.Run("RawError - service ID in device context", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), DvcIDKey{}, svcID)

		result, ok := GetDeviceIDFromContext(ctx)

		assert.True(t, ok) // Should work since both are *appioid.ID
		assert.NotNil(t, result)
		assert.Equal(t, svcID, result)
	})
}

func TestGetRoleFromContext(t *testing.T) {
	t.Run("Success - role found in context", func(t *testing.T) {
		role := roles.Api
		ctx := context.WithValue(context.Background(), roleKey{}, role)

		result, ok := GetRoleFromContext(ctx)

		assert.True(t, ok)
		assert.Equal(t, role, result)
	})

	t.Run("Success - different roles", func(t *testing.T) {
		testRoles := []roles.Role{
			roles.Api,
			roles.ApiDemo,
			roles.AppAppioSo,
			roles.DemoAppioSo,
			roles.IOS,
			roles.Unknown,
		}

		for _, role := range testRoles {
			t.Run("Role: "+string(role), func(t *testing.T) {
				ctx := context.WithValue(context.Background(), roleKey{}, role)

				result, ok := GetRoleFromContext(ctx)

				assert.True(t, ok)
				assert.Equal(t, role, result)
			})
		}
	})

	t.Run("RawError - role not found in context", func(t *testing.T) {
		ctx := context.Background()

		result, ok := GetRoleFromContext(ctx)

		assert.False(t, ok)
		assert.Equal(t, roles.Role(""), result) // Should be zero value
	})

	t.Run("RawError - wrong type in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), roleKey{}, "invalid-type")

		result, ok := GetRoleFromContext(ctx)

		assert.False(t, ok)
		assert.Equal(t, roles.Role(""), result)
	})

	t.Run("RawError - nil value in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), roleKey{}, nil)

		result, ok := GetRoleFromContext(ctx)

		assert.False(t, ok)
		assert.Equal(t, roles.Role(""), result)
	})

	t.Run("Success - empty role", func(t *testing.T) {
		role := roles.Role("")
		ctx := context.WithValue(context.Background(), roleKey{}, role)

		result, ok := GetRoleFromContext(ctx)

		assert.True(t, ok)
		assert.Equal(t, role, result)
	})
}

func TestContextKeys_TypeSafety(t *testing.T) {
	t.Run("Context keys are different types", func(t *testing.T) {
		svcKey := SvcIDKey{}
		dvcKey := DvcIDKey{}
		rKey := roleKey{}

		// These should be different types
		assert.IsType(t, SvcIDKey{}, svcKey)
		assert.IsType(t, DvcIDKey{}, dvcKey)
		assert.IsType(t, roleKey{}, rKey)

		// They should not be equal
		assert.NotEqual(t, svcKey, dvcKey)
		assert.NotEqual(t, svcKey, rKey)
		assert.NotEqual(t, dvcKey, rKey)
	})

	t.Run("Multiple values in same context", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		role := roles.Api

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, svcID)
		ctx = context.WithValue(ctx, DvcIDKey{}, dvcID)
		ctx = context.WithValue(ctx, roleKey{}, role)

		// All values should be retrievable
		resultSvcID, svcOK := GetServiceIDFromContext(ctx)
		resultDvcID, dvcOK := GetDeviceIDFromContext(ctx)
		resultRole, roleOK := GetRoleFromContext(ctx)

		assert.True(t, svcOK)
		assert.True(t, dvcOK)
		assert.True(t, roleOK)
		assert.Equal(t, svcID, resultSvcID)
		assert.Equal(t, dvcID, resultDvcID)
		assert.Equal(t, role, resultRole)
	})

	t.Run("Context key isolation", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		// Set service ID but try to get device ID
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		resultDvcID, dvcOK := GetDeviceIDFromContext(ctx)
		resultRole, roleOK := GetRoleFromContext(ctx)

		assert.False(t, dvcOK)
		assert.False(t, roleOK)
		assert.Nil(t, resultDvcID)
		assert.Equal(t, roles.Role(""), resultRole)
	})
}
