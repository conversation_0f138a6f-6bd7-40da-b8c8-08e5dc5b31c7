package middlewares

import (
	"encoding/json"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestHeaderToContextIDMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that checks if the context has the expected values
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - valid service ID in header", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			ctxSvcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.True(t, ok)
			assert.Equal(t, svcID, ctxSvcID)
			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", svcID.String())
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Success - valid device ID in header", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			ctxDvcID, ok := ctx.Value(DvcIDKey{}).(*appioid.ID)
			assert.True(t, ok)
			assert.Equal(t, dvcID, ctxDvcID)
			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Device-Id", DvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Device-Id", dvcID.String())
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Success - no header provided", func(t *testing.T) {
		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			ctxSvcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.False(t, ok)
			assert.Nil(t, ctxSvcID)
			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Success - empty header value", func(t *testing.T) {
		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			ctxSvcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.False(t, ok)
			assert.Nil(t, ctxSvcID)
			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RawError - invalid ID format", func(t *testing.T) {
		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "invalid-id-format")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Success - device ID in service ID header", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			ctxID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.True(t, ok)
			assert.Equal(t, dvcID, ctxID)
			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", dvcID.String()) // Device ID in service ID header - should work
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Different header names", func(t *testing.T) {
		testCases := []struct {
			name       string
			headerName string
			contextKey interface{}
			idValue    string
		}{
			{"X-Service-Id header", "X-Service-Id", SvcIDKey{}, "svc_00000000000000000000000001"},
			{"X-Device-Id header", "X-Device-Id", DvcIDKey{}, "dvc_00000000000000000000000001"},
			{"Custom header name", "Custom-ID-Header", SvcIDKey{}, "svc_00000000000000000000000002"},
			{"Another custom header", "My-Device-Header", DvcIDKey{}, "dvc_00000000000000000000000002"},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				expectedID := appioid.MustParse(tc.idValue)

				// Handler that checks context values
				contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					ctx := r.Context()
					ctxID, ok := ctx.Value(tc.contextKey).(*appioid.ID)
					assert.True(t, ok)
					assert.Equal(t, expectedID, ctxID)
					w.WriteHeader(http.StatusOK)
				})

				middleware := HeaderToContextIDMiddleware(tc.headerName, tc.contextKey, logger)
				handler := middleware(contextCheckHandler)

				req := httptest.NewRequest("GET", "/test", nil)
				req.Header.Set(tc.headerName, tc.idValue)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
			})
		}
	})

	t.Run("Multiple headers in same request", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		// Handler that checks both context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			ctxSvcID, svcOK := ctx.Value(SvcIDKey{}).(*appioid.ID)
			ctxDvcID, dvcOK := ctx.Value(DvcIDKey{}).(*appioid.ID)

			assert.True(t, svcOK)
			assert.True(t, dvcOK)
			assert.Equal(t, svcID, ctxSvcID)
			assert.Equal(t, dvcID, ctxDvcID)

			w.WriteHeader(http.StatusOK)
		})

		// Chain multiple middlewares
		svcMiddleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		dvcMiddleware := HeaderToContextIDMiddleware("X-Device-Id", DvcIDKey{}, logger)
		handler := svcMiddleware(dvcMiddleware(contextCheckHandler))

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", svcID.String())
		req.Header.Set("X-Device-Id", dvcID.String())
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Context key isolation", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		// Handler that checks only service ID is set
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			ctxSvcID, svcOK := ctx.Value(SvcIDKey{}).(*appioid.ID)
			ctxDvcID, dvcOK := ctx.Value(DvcIDKey{}).(*appioid.ID)

			assert.True(t, svcOK)
			assert.False(t, dvcOK)
			assert.Equal(t, svcID, ctxSvcID)
			assert.Nil(t, ctxDvcID)

			w.WriteHeader(http.StatusOK)
		})

		middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", svcID.String())
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Invalid ID formats", func(t *testing.T) {
		testCases := []string{
			"not-an-id",
			"svc_invalid",
			"svc_",
			"svc_tooshort",
			"svc_00000000000000000000000001extra",
			"123",
			"svc_gggggggggggggggggggggggggg", // Invalid characters
		}

		for _, invalidID := range testCases {
			t.Run("Invalid ID: "+invalidID, func(t *testing.T) {
				middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
				handler := middleware(mockHandler)

				req := httptest.NewRequest("GET", "/test", nil)
				req.Header.Set("X-Service-Id", invalidID)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				assert.Equal(t, http.StatusBadRequest, w.Code)
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Contains(t, response, "error")
			})
		}

		// Test empty header separately
		t.Run("Empty header", func(t *testing.T) {
			middleware := HeaderToContextIDMiddleware("X-Service-Id", SvcIDKey{}, logger)
			handler := middleware(mockHandler)

			req := httptest.NewRequest("GET", "/test", nil)
			req.Header.Set("X-Service-Id", "")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			// Empty header should be ignored, not cause error
			assert.Equal(t, http.StatusOK, w.Code)
		})
	})
}
