package middlewares

import (
	"context"
	"go.uber.org/zap"
	"net/http"
)

// HeaderToContextMiddleware is a generic middleware that parses a header value using the provided parser
// and sets it in the request context. If a default value is provided, it will be used when the header
// is missing or invalid. If no default is provided, HTTP 400 is returned for invalid headers.
func HeaderToContextMiddleware[T any](
	headerName string,
	contextKey any,
	parser func(string) (T, error),
	logger *zap.Logger,
	defaultValue ...T,
) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			headerValue := r.Header.Get(headerName)
			hasDefault := len(defaultValue) > 0

			if headerValue != "" {
				parsed, err := parser(headerValue)
				if err != nil {
					if hasDefault {
						ctx = context.WithValue(ctx, contextKey, defaultValue[0])
					} else {
						logger.Error("invalid header value", zap.String("header", headerName), zap.String("value", headerValue), zap.Error(err))
						http.Error(w, "Invalid "+headerName+" header", http.StatusBadRequest)
						return
					}
				} else {
					ctx = context.WithValue(ctx, contextKey, parsed)
				}
			}

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
