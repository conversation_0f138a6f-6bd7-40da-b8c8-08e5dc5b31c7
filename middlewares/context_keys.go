package middlewares

import (
	"context"

	"api.appio.so/models"
	"api.appio.so/pkg/roles"
	"github.com/appio-so/go-appioid"
)

// Define context keys
type PlatformKey struct{}
type SvcID<PERSON><PERSON> struct{}
type Dvc<PERSON><PERSON><PERSON> struct{}
type OrgID<PERSON>ey struct{}
type roleKey struct{}

func GetPlatformFromContext(ctx context.Context) (models.Platform, bool) {
	platform, ok := ctx.Value(PlatformKey{}).(models.Platform)
	if !ok {
		return "", false
	}
	return platform, ok
}

func GetServiceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	svcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return svcID, ok
}

func GetDeviceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	dvcID, ok := ctx.Value(DvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return dvcID, ok
}

func GetOrganizationIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	orgID, ok := ctx.Value(OrgIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return orgID, ok
}

func GetRoleFromContext(ctx context.Context) (roles.Role, bool) {
	role, ok := ctx.Value(roleKey{}).(roles.Role)
	return role, ok
}
