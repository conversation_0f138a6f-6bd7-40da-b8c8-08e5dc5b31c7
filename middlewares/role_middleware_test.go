package middlewares

import (
	"api.appio.so/pkg/roles"
	"context"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestRoleMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that should be called when access is granted
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write<PERSON>eader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - user has required role", func(t *testing.T) {
		middleware := RoleMiddleware(logger, roles.Api)
		handler := middleware(mockHandler)

		ctx := context.WithValue(context.Background(), roleKey{}, roles.Api)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - user has one of multiple required roles", func(t *testing.T) {
		middleware := RoleMiddleware(logger, roles.Api, roles.ApiDemo, roles.IOS)
		handler := middleware(mockHandler)

		ctx := context.WithValue(context.Background(), roleKey{}, roles.IOS)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("RawError - no role in context", func(t *testing.T) {
		middleware := RoleMiddleware(logger, roles.Api)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("RawError - user does not have required role", func(t *testing.T) {
		middleware := RoleMiddleware(logger, roles.Api)
		handler := middleware(mockHandler)

		ctx := context.WithValue(context.Background(), roleKey{}, roles.IOS)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("RawError - user does not have any of multiple required roles", func(t *testing.T) {
		middleware := RoleMiddleware(logger, roles.Api, roles.ApiDemo)
		handler := middleware(mockHandler)

		ctx := context.WithValue(context.Background(), roleKey{}, roles.IOS)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Success - single role requirement", func(t *testing.T) {
		testCases := []struct {
			name         string
			userRole     roles.Role
			requiredRole roles.Role
			shouldPass   bool
		}{
			{"Api user accessing Api endpoint", roles.Api, roles.Api, true},
			{"ApiDemo user accessing ApiDemo endpoint", roles.ApiDemo, roles.ApiDemo, true},
			{"IOS user accessing IOS endpoint", roles.IOS, roles.IOS, true},
			{"AppAppioSo user accessing AppAppioSo endpoint", roles.AppAppioSo, roles.AppAppioSo, true},
			{"DemoAppioSo user accessing DemoAppioSo endpoint", roles.DemoAppioSo, roles.DemoAppioSo, true},
			{"Api user accessing IOS endpoint", roles.Api, roles.IOS, false},
			{"IOS user accessing Api endpoint", roles.IOS, roles.Api, false},
			{"Unknown user accessing Api endpoint", roles.Unknown, roles.Api, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				middleware := RoleMiddleware(logger, tc.requiredRole)
				handler := middleware(mockHandler)

				ctx := context.WithValue(context.Background(), roleKey{}, tc.userRole)
				req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				if tc.shouldPass {
					assert.Equal(t, http.StatusOK, w.Code)
					assert.Equal(t, "success", w.Body.String())
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Contains(t, response, "error")
				}
			})
		}
	})

	t.Run("Multiple roles requirement", func(t *testing.T) {
		testCases := []struct {
			name          string
			userRole      roles.Role
			requiredRoles []roles.Role
			shouldPass    bool
		}{
			{"Api user with Api or IOS required", roles.Api, []roles.Role{roles.Api, roles.IOS}, true},
			{"IOS user with Api or IOS required", roles.IOS, []roles.Role{roles.Api, roles.IOS}, true},
			{"ApiDemo user with Api or IOS required", roles.ApiDemo, []roles.Role{roles.Api, roles.IOS}, false},
			{"AppAppioSo user with multiple app roles", roles.AppAppioSo, []roles.Role{roles.AppAppioSo, roles.DemoAppioSo}, true},
			{"DemoAppioSo user with multiple app roles", roles.DemoAppioSo, []roles.Role{roles.AppAppioSo, roles.DemoAppioSo}, true},
			{"Api user with multiple app roles", roles.Api, []roles.Role{roles.AppAppioSo, roles.DemoAppioSo}, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				middleware := RoleMiddleware(logger, tc.requiredRoles...)
				handler := middleware(mockHandler)

				ctx := context.WithValue(context.Background(), roleKey{}, tc.userRole)
				req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				if tc.shouldPass {
					assert.Equal(t, http.StatusOK, w.Code)
					assert.Equal(t, "success", w.Body.String())
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Contains(t, response, "error")
				}
			})
		}
	})

	t.Run("Empty required roles", func(t *testing.T) {
		middleware := RoleMiddleware(logger)
		handler := middleware(mockHandler)

		ctx := context.WithValue(context.Background(), roleKey{}, roles.Api)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// With no required roles, access should be denied
		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

func TestHasAccess(t *testing.T) {
	t.Run("User has required role", func(t *testing.T) {
		result := hasAccess(roles.Api, []roles.Role{roles.Api})
		assert.True(t, result)
	})

	t.Run("User has one of multiple required roles", func(t *testing.T) {
		result := hasAccess(roles.IOS, []roles.Role{roles.Api, roles.IOS, roles.ApiDemo})
		assert.True(t, result)
	})

	t.Run("User does not have required role", func(t *testing.T) {
		result := hasAccess(roles.Api, []roles.Role{roles.IOS})
		assert.False(t, result)
	})

	t.Run("User does not have any of multiple required roles", func(t *testing.T) {
		result := hasAccess(roles.Unknown, []roles.Role{roles.Api, roles.IOS, roles.ApiDemo})
		assert.False(t, result)
	})

	t.Run("Empty required roles", func(t *testing.T) {
		result := hasAccess(roles.Api, []roles.Role{})
		assert.False(t, result)
	})

	t.Run("All role combinations", func(t *testing.T) {
		allRoles := []roles.Role{roles.Unknown, roles.Api, roles.ApiDemo, roles.AppAppioSo, roles.DemoAppioSo, roles.IOS}

		for _, userRole := range allRoles {
			for _, requiredRole := range allRoles {
				t.Run("User: "+string(userRole)+" Required: "+string(requiredRole), func(t *testing.T) {
					result := hasAccess(userRole, []roles.Role{requiredRole})
					expected := userRole == requiredRole
					assert.Equal(t, expected, result)
				})
			}
		}
	})

	t.Run("Nil required roles", func(t *testing.T) {
		result := hasAccess(roles.Api, nil)
		assert.False(t, result)
	})
}
