package middlewares

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestAuthRoleService(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that checks if the context has the expected values
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - valid API key from database", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

		// Mock repository to return true for valid API key
		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), "valid-api-key_12345678901234567890123456789012345678901234567890").
			Return(true)

		authConfig := &config.AuthConfig{
			App:  "app-key",
			IOS:  "ios-key",
			Demo: "demo-key",
		}

		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer valid-api-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid app API key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)

		authConfig := &config.AuthConfig{
			App:  "app-key_12345678901234567890123456789012345678901234567890",
			IOS:  "ios-key",
			Demo: "demo-key",
		}

		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer app-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("RawError - missing Authorization header", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("RawError - Authorization header without Bearer prefix", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Basic dXNlcjpwYXNz")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("RawError - empty Bearer token", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("Success - inactive API key returns Unknown role", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}

		// Mock repository to return false for inactive key
		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), "inactive-key_12345678901234567890123456789012345678901234567890").
			Return(false)

		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer inactive-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// When API key is not active, service returns Unknown role with no error
		// The middleware should still call next handler since it's a valid response
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	// TODO: TESTING disabled svcID from api_keys db
	//t.Run("Context values are set correctly", func(t *testing.T) {
	//	ctrl := gomock.NewController(t)
	//	defer ctrl.Finish()
	//
	//	mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	//	svcID := appioid.MustParse("svc_00000000000000000000000001")
	//	authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
	//
	//	mockRepo.EXPECT().
	//		IsActiveAPIKey(gomock.Any(), "test-key_12345678901234567890123456789012345678901234567890").
	//		Return(svcID, nil)
	//
	//	// Handler that checks context values
	//	contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	//		ctx := r.Context()
	//
	//		// Check service ID in context
	//		ctxSvcID, ok := GetServiceIDFromContext(ctx)
	//		assert.True(t, ok)
	//		assert.Equal(t, svcID, ctxSvcID)
	//
	//		// Check role in context
	//		ctxRole, ok := GetRoleFromContext(ctx)
	//		assert.True(t, ok)
	//		assert.Equal(t, roles.Api, ctxRole)
	//
	//		w.WriteHeader(http.StatusOK)
	//	})
	//
	//	mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
	//	apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
	//	middleware := AuthRoleService(apiKeyService, mockJWTService)
	//	handler := middleware(contextCheckHandler)
	//
	//	req := httptest.NewRequest("GET", "/test", nil)
	//	req.Header.Set("Authorization", "Bearer test-key_12345678901234567890123456789012345678901234567890")
	//	w := httptest.NewRecorder()
	//
	//	handler.ServeHTTP(w, req)
	//
	//	assert.Equal(t, http.StatusOK, w.Code)
	//})

	t.Run("Context values when service ID is nil", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{
			App:  "app-key",
			IOS:  "ios-key_12345678901234567890123456789012345678901234567890",
			Demo: "demo-key",
		}

		// Handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Check service ID is not in context
			ctxSvcID, ok := GetServiceIDFromContext(ctx)
			assert.False(t, ok)
			assert.Nil(t, ctxSvcID)

			// Check role in context
			ctxRole, ok := GetRoleFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, roles.IOS, ctxRole)

			w.WriteHeader(http.StatusOK)
		})

		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ios-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RawError - API key too short", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer short-key") // Too short
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Success - database returns false for unknown key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}

		// Mock repository to return false for unknown key
		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), "unknown-key_12345678901234567890123456789012345678901234567890").
			Return(false)

		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)
		middleware := AuthRoleService(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer unknown-key_12345678901234567890123456789012345678901234567890")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Unknown API key gets Unknown role but middleware continues
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})
}
