# Setup
@host = http://localhost:8082

###
# @name LIST DEVICES (subscribed to a service by api key)
GET {{host}}/v1/devices
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain devices array with exact device data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length === 2, `Devices array length was: ${response.body.length}`)

        // First device - iPad Pro
        const device1 = response.body[0]
        client.assert(device1.id === "dvc_11111111111111111111111111", `Device 1 ID was: ${device1.id}`)
        client.assert(device1.user_id === "ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a", `Device 1 user_id was: ${device1.user_id}`)
        client.assert(device1.name === "iPad Pro 13-inch", `Device 1 name was: ${device1.name}`)
        client.assert(device1.platform === "ios", `Device 1 platform was: ${device1.platform}`)
        client.assert(device1.os_version === "17.1", `Device 1 os_version was: ${device1.os_version}`)
        client.assert(device1.model === "iPad", `Device 1 model was: ${device1.model}`)
        client.assert(device1.device_token === "", `Device 1 device_token was: ${device1.device_token}`)
        client.assert(device1.notifications_enabled === false, `Device 1 notifications_enabled was: ${device1.notifications_enabled}`)
        client.assert(device1.device_identifier === "iPad7,6", `Device 1 device_identifier was: ${device1.device_identifier}`)
        client.assert(device1.marketing_name === "", `Device 1 marketing_name was: ${device1.marketing_name}`)

        // Second device - Android Phone
        const device2 = response.body[1]
        client.assert(device2.id === "dvc_00000000000000000000000000", `Device 2 ID was: ${device2.id}`)
        client.assert(device2.user_id === "ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a", `Device 2 user_id was: ${device2.user_id}`)
        client.assert(device2.name === "My Personal Phone", `Device 2 name was: ${device2.name}`)
        client.assert(device2.platform === "android", `Device 2 platform was: ${device2.platform}`)
        client.assert(device2.os_version === "14 (API 34)", `Device 2 os_version was: ${device2.os_version}`)
        client.assert(device2.model === "samsung┼SM-A136B", `Device 2 model was: ${device2.model}`)
        client.assert(device2.device_token === "*** token ***", `Device 2 device_token was: ${device2.device_token}`)
        client.assert(device2.notifications_enabled === true, `Device 2 notifications_enabled was: ${device2.notifications_enabled}`)
        client.assert(device2.device_identifier === "6874387efd0adcda", `Device 2 device_identifier was: ${device2.device_identifier}`)
        client.assert(device2.marketing_name === "", `Device 2 marketing_name was: ${device2.marketing_name}`)
    })
%}

###
# @name LIST DEVICES as demo.appio.so
GET {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain devices array with exact device data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Devices array length was: ${response.body.length}`)

        response.body.forEach((device, index) => {
            client.assert(device.id && device.id.startsWith('dvc_'), `Device ${index} ID was: ${device.id}`)

            if (device.user_id === "demo") {
                // Demo devices have consistent values
                client.assert(device.name === "demo", `Device ${index} name was: ${device.name}`)
                client.assert(device.platform === "ios", `Device ${index} platform was: ${device.platform}`)
                client.assert(device.os_version === "demo", `Device ${index} os_version was: ${device.os_version}`)
                client.assert(device.model === "demo", `Device ${index} model was: ${device.model}`)
                client.assert(device.device_token === "", `Device ${index} device_token was: ${device.device_token}`)
                client.assert(device.notifications_enabled === true, `Device ${index} notifications_enabled was: ${device.notifications_enabled}`)
                client.assert(device.device_identifier === "demo", `Device ${index} device_identifier was: ${device.device_identifier}`)
                client.assert(device.marketing_name === "", `Device ${index} marketing_name was: ${device.marketing_name}`)
            } else if (device.id === "dvc_11111111111111111111111111") {
                // The iPad device
                client.assert(device.user_id === "another-customer-user-id", `iPad device user_id was: ${device.user_id}`)
                client.assert(device.name === "iPad Pro 13-inch", `iPad device name was: ${device.name}`)
                client.assert(device.platform === "ios", `iPad device platform was: ${device.platform}`)
                client.assert(device.os_version === "17.1", `iPad device os_version was: ${device.os_version}`)
                client.assert(device.model === "iPad", `iPad device model was: ${device.model}`)
                client.assert(device.device_token === "", `iPad device device_token was: ${device.device_token}`)
                client.assert(device.notifications_enabled === false, `iPad device notifications_enabled was: ${device.notifications_enabled}`)
                client.assert(device.device_identifier === "iPad7,6", `iPad device device_identifier was: ${device.device_identifier}`)
                client.assert(device.marketing_name === "", `iPad device marketing_name was: ${device.marketing_name}`)
            }
        })
    })
%}

###
# @name CREATE & LINK DEVICE as ios
POST {{host}}/mobile/devices
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "customer_user_id": "cli-test",
  "name": "iPhone 16E",
  "platform": "ios",
  "os_version": "18.3.1",
  "model": "iPhone",
  "device_identifier": "iPhone11,8",
  "notifications_enabled": true
}

> {%
    client.global.set("ios_device_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE & LINK DEVICE as demo.appio.so
POST {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "customer_user_id": "demo",
  "name": "demo",
  "platform": "ios",
  "os_version": "demo",
  "model": "demo",
  "device_identifier": "demo",
  "notifications_enabled": true
}

> {%
    client.global.set("demo_device_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name GET DEVICE (subscribed to a service by api key)
GET {{host}}/v1/devices/dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact device data", () => {
        client.assert(response.body.id === "dvc_00000000000000000000000000", `Device ID was: ${response.body.id}`)
        client.assert(response.body.user_id === "ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a", `User ID was: ${response.body.user_id}`)
        client.assert(response.body.name === "My Personal Phone", `Device name was: ${response.body.name}`)
        client.assert(response.body.platform === "android", `Platform was: ${response.body.platform}`)
        client.assert(response.body.os_version === "14 (API 34)", `OS version was: ${response.body.os_version}`)
        client.assert(response.body.model === "samsung┼SM-A136B", `Model was: ${response.body.model}`)
        client.assert(response.body.device_identifier === "6874387efd0adcda", `Device identifier was: ${response.body.device_identifier}`)
        client.assert(response.body.notifications_enabled === true, `Notifications enabled was: ${response.body.notifications_enabled}`)
        client.assert(response.body.device_token === "*** token ***", `Device token was: ${response.body.device_token}`)
        client.assert(response.body.marketing_name === "", `Marketing name was: ${response.body.marketing_name}`)
    })
%}

###
# @name GET DEVICE as ios
GET {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact device data", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
        client.assert(response.body.user_id === "cli-test", `User ID was: ${response.body.user_id}`)
        client.assert(response.body.name === "iPhone 16E", `Device name was: ${response.body.name}`)
        client.assert(response.body.platform === "ios", `Platform was: ${response.body.platform}`)
        client.assert(response.body.os_version === "18.3.1", `OS version was: ${response.body.os_version}`)
        client.assert(response.body.model === "iPhone", `Model was: ${response.body.model}`)
        client.assert(response.body.device_identifier === "iPhone11,8", `Device identifier was: ${response.body.device_identifier}`)
        client.assert(response.body.notifications_enabled === true, `Notifications enabled was: ${response.body.notifications_enabled}`)
        client.assert(response.body.device_token === "", `Device token was: ${response.body.device_token}`)
        client.assert(response.body.marketing_name === "iPhone XR", `Marketing name was: ${response.body.marketing_name}`)
    })
%}

###
# @name GET DEVICE - not found (subscribed to a service by api key)
GET {{host}}/v1/devices/dvc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name LINK DEVICE WITH SERVICE as ios
POST {{host}}/mobile/devices/{{ios_device_id}}/services
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_11111111111111111111111111

{
  "customer_user_id": "demo-cli-test"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name UPDATE DEVICE as ios
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": true,
  "device_token": "80d00eefc688e8dd1071aa3aee592c04aa9de60b88a0e88eb9bf9c527d8609154a058d77dac9fb32fe3382faa00d57bba0b97f61b3687025392db90b9c3c50d2e7b2b282e10cbcb31a9c5d357afbcdcc"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name UPDATE DEVICE as ios - not found
PATCH {{host}}/mobile/devices/dvc_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": false,
  "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name DELETE DEVICE (disconnect from service, subscribed to a service by api key)
DELETE {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name DELETE DEVICE - re-try fails (disconnect from service, subscribed to a service by api key)
DELETE {{host}}/v1/devices/{{ios_device_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name UPDATE DEVICE as ios - still works after delete/disconnect
PATCH {{host}}/mobile/devices/{{ios_device_id}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222

{
  "notifications_enabled": true,
  "device_token": "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name (create device for delete test below)
POST {{host}}/demo-appio-so/devices
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "customer_user_id": "demo2",
  "name": "demo2",
  "platform": "ios",
  "os_version": "demo2",
  "model": "demo2",
  "device_identifier": "demo2",
  "notifications_enabled": false
}

> {%
    client.global.set("demo_device_id_2", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name DELETE DEVICE as ios (disconnect from service)
DELETE {{host}}/mobile/devices/{{demo_device_id_2}}
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}



###
# @name (create device for delete test below)
POST {{host}}/mobile/devices
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000

{
  "customer_user_id": "demo3",
  "name": "demo3",
  "platform": "ios",
  "os_version": "demo3",
  "model": "demo3",
  "device_identifier": "demo3",
  "notifications_enabled": false
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('dvc_'), `Device ID was: ${response.body.id}`)
    })
%}

###
# @name DELETE DEVICES for user (disconnect from service)
DELETE {{host}}/v1/devices?user_id=demo3
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device service connections array", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 0, `Device service connections array length was: ${response.body.length}`)

        // Validate each device service connection object in the array
        response.body.forEach((connection, index) => {
            client.assert(connection.id && connection.id.startsWith('dvcsvc_'), `Connection ${index} ID was: ${connection.id}`)
        })
    })
%}