# Setup
@host = http://localhost:8082

# TODO TESTING add tests to verify that there is no access to data across organizations without proper auth

###
# @name LIST WIDGETS
GET {{host}}/v1/widgets
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain widgets array with exact widget data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length === 1, `Widgets array length was: ${response.body.length}`)

        // First widget - Ring widget
        const widget = response.body[0]
        client.assert(widget.id === "wgt_00000000000000000000000000", `Widget ID was: ${widget.id}`)
        client.assert(widget.service_id === "svc_00000000000000000000000000", `Widget service_id was: ${widget.service_id}`)
        client.assert(widget.template === "ring", `Widget template was: ${widget.template}`)
        client.assert(widget.source && widget.source.type === "static", `Widget source type was: ${widget.source?.type}`)
        client.assert(widget.source && widget.source.data === 10, `Widget source data was: ${widget.source?.data}`)
    })
%}

###
# @name GET WIDGET
GET {{host}}/v1/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact widget data", () => {
        client.assert(response.body.id === "wgt_00000000000000000000000000", `Widget ID was: ${response.body.id}`)
        client.assert(response.body.service_id === "svc_00000000000000000000000000", `Widget service_id was: ${response.body.service_id}`)
        client.assert(response.body.template === "ring", `Widget template was: ${response.body.template}`)
        client.assert(response.body.source && response.body.source.type === "static", `Widget source type was: ${response.body.source?.type}`)
        client.assert(response.body.source && response.body.source.data === 10, `Widget source data was: ${response.body.source?.data}`)
    })
%}

###
# @name GET WIDGET - not found
GET {{host}}/v1/widgets/wgt_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name GET WIDGETS as demo.appio.so - not found
GET {{host}}/demo-appio-so/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name GET WIDGET as ios
GET {{host}}/mobile/widgets/wgt_00000000000000000000000000
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-Service-Id: svc_00000000000000000000000000
#X-Device-Id: dvc_00000000000000000000000000   # not needed, but client might send it if it has it

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact iOS widget data", () => {
        client.assert(response.body.id === "wgt_00000000000000000000000000", `Widget ID was: ${response.body.id}`)
        client.assert(response.body.service_id === "svc_00000000000000000000000000", `Widget service_id was: ${response.body.service_id}`)
        client.assert(response.body.name === "Ring", `Widget name was: ${response.body.name}`)

        const expectedConfig = "\n{\n\t\"variants\": [\n\t\t{\n\t\t\t\"properties\": {\n\t\t\t\t\"version\": \"1.0\",\n\t\t\t\t\"supportedFamilies\": [\"systemSmall\", \"systemMedium\", \"systemLarge\", \"systemExtraLarge\"],\n\t\t\t\t\"background\": \"clear\"\n\t\t\t},\n\t\t\t\"elements\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"hstack\",\n\t\t\t\t\t\"properties\": {},\n\t\t\t\t\t\"elements\": [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"spacer\",\n\t\t\t\t\t\t\t\"properties\": {}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\"type\": \"lastUpdated\",\n\t\t\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\t\t\"color\": \"secondary\",\n\t\t\t\t\t\t\t\t\"fontSize\": 12,\n\t\t\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\t\t\"right\": 10\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"gauge\",\n\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\"value\": 10,\n\t\t\t\t\t\t\"currentValueLabel\": \"10\",\n\t\t\t\t\t\t\"style\": \"accessoryCircularCapacity\",\n\t\t\t\t\t\t\"color\": \"primary\",\n\t\t\t\t\t\t\"tint\": \"primary\",\n\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\"top\": 10,\n\t\t\t\t\t\t\t\"bottom\": 10\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"refreshButton\",\n\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\"text\": \"Refresh\",\n\t\t\t\t\t\t\"style\": \"borderedProminent\",\n\t\t\t\t\t\t\"color\": \"#fff\",\n\t\t\t\t\t\t\"padding\": {\n\t\t\t\t\t\t\t\"bottom\": 10\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t]\n}\n";
        client.assert(response.body.config === expectedConfig, `Widget config was: ${response.body.config}`)
    })
%}

###
# @name CREATE WIDGET
POST {{host}}/v1/widgets
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "template": "bar chart",
  "source": {
    "data": 123,
    "type": "static"
  }
}

> {%
    client.global.set("widget_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact widget ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('wgt_'), `Widget ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE WIDGET as demo.appio.so
POST {{host}}/demo-appio-so/widgets
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "bar chart"
}

> {%
    client.global.set("demo_widget_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact widget ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('wgt_'), `Widget ID was: ${response.body.id}`)
    })
%}

###
# @name UPDATE WIDGET
PATCH {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "template": "line chart"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact widget ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('wgt_'), `Widget ID was: ${response.body.id}`)
    })
%}

###
# @name UPDATE WIDGET as demo.appio.so
PATCH {{host}}/demo-appio-so/widgets/{{demo_widget_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "demo chart"
}

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact widget ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('wgt_'), `Widget ID was: ${response.body.id}`)
    })
%}

###
# @name UPDATE WIDGET as demo.appio.so - not found
PATCH {{host}}/demo-appio-so/widgets/{{widget_id}}
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "template": "demo chart"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name DELETE WIDGET
DELETE {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact deleted widget ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('wgt_'), `Widget ID was: ${response.body.id}`)
    })
%}

###
# @name DELETE WIDGET - retry fails
DELETE {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name UPDATE WIDGET - deleted fails
PATCH {{host}}/v1/widgets/{{widget_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "template": "line chart"
}

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}