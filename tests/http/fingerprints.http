# Setup
@host = http://localhost:8082

###
# @name LIST FINGERPRINTS
# n/a

###
# @name GET FINGERPRINT
# n/a

###
# @name CREATE FINGERPRINT as app.appio.so
POST {{host}}/app-appio-so/fingerprints
Authorization: Bearer dev_33333333333333333333333333333333333333333333333333

{
  "service_id": "svc_00000000000000000000000000",
  "customer_user_id": "customer_0000000000",
  "data": {
      "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1",
      "screen_resolution": "1792x1120",
      "language": "en-GB",
      "time_offset": 0
    }
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain fingerprint ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('fing_'), `Response body was: ${JSON.stringify(response.body)}`)
    })
%}

###
# @name MATCH FINGERPRINT as ios
POST {{host}}/mobile/fingerprints/match
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
#X-Device-Id: dvc_00000000000000000000000000   # not needed, but client might send it if it has it
X-App-Platform: ios

{
    "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
    "screen_resolution": "1792x1120",
    "language": "en-GB",
    "time_offset": 0
}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain fingerprint match data", () => {
            client.assert(response.body.fingerprint_id && response.body.fingerprint_id.startsWith('fing_'), `Fingerprint ID was: ${response.body.fingerprint_id}`)
            client.assert(response.body.service_id && response.body.service_id.startsWith('svc_'), `Service ID was: ${response.body.service_id}`)
            client.assert(response.body.customer_user_id, `Customer user ID was: ${response.body.customer_user_id}`)
        })
    }
%}

###
# @name UPDATE FINGERPRINT
# n/a

###
# @name DELETE FINGERPRINT
# n/a



###
# @name MATCH FINGERPRINT as android
POST {{host}}/mobile/fingerprints/match
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-App-Platform: android

{
  "user_agent": "unknown",
  "screen_resolution": "720x1465",
  "language": "en-GB",
  "time_offset": 0
}

> {%
    client.test("Response status should be 200 or 404", () => {
        client.assert([200, 404].includes(response.status), `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    if (response.status === 200) {
        client.test("Response body should contain fingerprint match data", () => {
            client.assert(response.body.fingerprint_id && response.body.fingerprint_id.startsWith('fing_'), `Fingerprint ID was: ${response.body.fingerprint_id}`)
            client.assert(response.body.service_id && response.body.service_id.startsWith('svc_'), `Service ID was: ${response.body.service_id}`)
            client.assert(response.body.customer_user_id, `Customer user ID was: ${response.body.customer_user_id}`)
        })
    } else if (response.status === 404) {
        client.test("404 response should contain error message", () => {
            client.assert(response.body.error && response.body.error.message, `Error message was: ${JSON.stringify(response.body)}`)
        })
    }
%}