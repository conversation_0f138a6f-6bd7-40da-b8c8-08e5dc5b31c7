package database

import (
	"context"
	"fmt"

	"api.appio.so/pkg/config"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

type PGXQueryTracer struct {
	logger *zap.Logger
}

// TraceQueryStart is called at the beginning of Query, QueryRow, and Exec calls.
func (t *PGXQueryTracer) TraceQueryStart(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryStartData) context.Context {
	t.logger.Info("query started", zap.String("sql", data.SQL), zap.Any("args", data.Args))
	return ctx
}

// TraceQueryEnd is called at the end of the query execution.
func (t *PGXQueryTracer) TraceQueryEnd(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryEndData) {
	if data.Err != nil {
		t.logger.Error("query ended with error", zap.Error(data.Err))
	} else {
		t.logger.Info("query ended successfully", zap.String("command_tag", data.CommandTag.String()))
	}
}

func InitDatabase(ctx context.Context, cfg *config.DBConfig, logger *zap.Logger) (*pgxpool.Pool, *pgxpool.Pool, error) {
	var appioPool, fingPool *pgxpool.Pool

	// Appio
	if cfg.Source != "" {
		appioConfig, err := pgxpool.ParseConfig(cfg.Source)
		if err != nil {
			return nil, nil, fmt.Errorf("parsing appio db config: %w", err)
		}
		if cfg.LogQueries {
			appioConfig.ConnConfig.Tracer = &PGXQueryTracer{logger: logger}
		}

		appioPool, err = pgxpool.NewWithConfig(ctx, appioConfig)
		if err != nil {
			return nil, nil, fmt.Errorf("connecting to databas appioe: %w", err)
		}

		err = appioPool.Ping(context.Background())
		if err != nil {
			return nil, nil, fmt.Errorf("pinging database appio: %w", err)
		}

		_, err = appioPool.Exec(ctx, `SET TIME ZONE 'UTC'`)
		if err != nil {
			return nil, nil, fmt.Errorf("setting timezone to UTC for database appio: %w", err)
		}
	}

	// Fingerprints
	if cfg.SourceFing != "" {
		fingConfig, err := pgxpool.ParseConfig(cfg.SourceFing)
		if err != nil {
			return nil, nil, fmt.Errorf("parsing fingerprint db config: %w", err)
		}
		if cfg.LogQueries {
			fingConfig.ConnConfig.Tracer = &PGXQueryTracer{logger: logger}
		}

		fingPool, err = pgxpool.NewWithConfig(ctx, fingConfig)
		if err != nil {
			return nil, nil, fmt.Errorf("connecting to database fingerprints: %w", err)
		}

		err = fingPool.Ping(context.Background())
		if err != nil {
			return nil, nil, fmt.Errorf("pinging database fingerprints: %w", err)
		}

		_, err = fingPool.Exec(ctx, `SET TIME ZONE 'UTC'`)
		if err != nil {
			return nil, nil, fmt.Errorf("setting timezone to UTC for database fingerprints: %w", err)
		}
	}

	return appioPool, fingPool, nil
}
