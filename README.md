api.appio.so
============

Main API for private and public use.
Only JSON communication.
Every request expects `service_id` apart from creating a new service.

## Routing
- `/mobile/*` is used by iOS and Android apps. No authentication is used.
- `/demo-appio-so/*` is used by demo.appio.so app. Specific auth key is needed.
- `/app-appio-so/*` is used by app.appio.so app. Specific auth key is needed.
- `/v1/*` is a public API. Each user has his own auth key paired with their service_id. 
  API keys starting with `demo_` are identifying DemoAppioSo user and Service<PERSON> is the last 26 characters of the key.
- `/hi` is used for API docs. Requires auth key.
- `/health-check` is used for liveness and readiness checks. No auth key is needed.

## Release process

```shell
cd /var/www/api.appio.so/be
make release
```

## Cron

Cron is used for:
- Device names import
- Notification queuing
- Notification delivery
- Notification delivery cleanup
- Docs reset

### Manual job execution

```shell
make reset-docs
make import-device-names
```

## AWS Server

- App runs via systemctl service defined at `/etc/systemd/system/appio.api.service` based on `configs/appio.api.service`
- Service is set to start on boot `sudo systemctl enable appio.api`. Can be checked by `sudo systemctl is-enabled appio.api`
- App uses standard systemctl commands: `sudo systemctl start appio.api` (stop, restart, status)
- Logs are viewable via `sudo journalctl -u appio.api` add `-f` for full logs. add `--no-pager` to view full log

- Cron runs via systemctl service defined at `/etc/systemd/system/appio.api.cron.service` based on `configs/appio.api.cron.service`
- Service is set to start on boot `sudo systemctl enable appio.api.cron`. Can be checked by `sudo systemctl is-enabled appio.api.cron`
- Cron uses standard systemctl commands: `sudo systemctl start appio.api.cron` (stop, restart, status)
- Logs are viewable via `sudo journalctl -u appio.api.cron` add `-f` for full logs. add `--no-pager` to view full log

- Production config has to exist at `configs/config.toml` based on `configs/config.toml.dist`
- Webserver is Caddy and uses prod config from `configs/Caddyfile`
- Server has to have access to private repositories hosted on GitHub:
    ```shell
    echo "export GOPRIVATE=github.com/appio-so/*" >> ~/.bash_profile && \
    source ~/.bash_profile
    
    git config --global url."**************:".insteadOf "https://github.com/"
    ```

## AWS DB

We have 2 separate databases: `appio`, `fingerprint`.
Both have the same credentials for now.

- Database has to accept connections via localhost. `sudo vi /var/lib/pgsql/data/pg_hba.conf`
- DB user and tables are defined in `db/appio.sql`
- Local DB is run via docker and make command from root `make db-start`, destroyed via `make db-destroy` and accessed via `make db-connect`


# Future Improvements

- OpenAPI spec: https://packagemain.tech/p/practical-openapi-in-golang  tools: https://openapi.tools/