package repositories

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type APIKeyRepositoryInterface interface {
	IsActiveAPIKey(ctx context.Context, apiKey string) bool
}

type APIKeyRepository struct {
	DB *pgxpool.Pool
}

func NewAPIKeyRepository(db *pgxpool.Pool) *APIKeyRepository {
	return &APIKeyRepository{
		DB: db,
	}
}

func (r *APIKeyRepository) IsActiveAPIKey(ctx context.Context, apiKey string) bool {
	query := `
		SELECT EXISTS (
			SELECT 1 FROM api_keys
			WHERE api_key=@api_key AND deactivated_at IS NULL
		)`
	args := pgx.NamedArgs{"api_key": apiKey}
	var active bool
	if err := r.DB.QueryRow(ctx, query, args).Scan(&active); err != nil {
		return false
	}
	return active
}
