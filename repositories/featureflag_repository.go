package repositories

import (
	"api.appio.so/models"
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type FeatureFlagRepositoryInterface interface {
	GetBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error)
}

type FeatureFlagRepository struct {
	DB *pgxpool.Pool
}

func NewFeatureFlagRepository(db *pgxpool.Pool) *FeatureFlagRepository {
	return &FeatureFlagRepository{
		DB: db,
	}
}

func (r *FeatureFlagRepository) GetBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error) {
	var ff models.FeatureFlag
	var query = `SELECT id, platform, version, config FROM feature_flags WHERE platform=@platform AND version=@version`
	args := pgx.NamedArgs{
		"platform": platform,
		"version":  version,
	}
	err := r.DB.QueryRow(ctx, query, args).Scan(
		&ff.ID,
		&ff.Platform,
		&ff.Version,
		&ff.Config,
	)
	if err != nil {
		return nil, fmt.Errorf("getting feature flag by platform %s and version %s: %w", platform, version, err)
	}
	return &ff, nil
}
