// Code generated by MockGen. DO NOT EDIT.
// Source: services/featureflag_service.go
//
// Generated by this command:
//
//	mockgen -source=services/featureflag_service.go -destination=internal/mocks/mock_featureflag_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	gomock "go.uber.org/mock/gomock"
)

// MockFeatureFlagServiceInterface is a mock of FeatureFlagServiceInterface interface.
type MockFeatureFlagServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFeatureFlagServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockFeatureFlagServiceInterfaceMockRecorder is the mock recorder for MockFeatureFlagServiceInterface.
type MockFeatureFlagServiceInterfaceMockRecorder struct {
	mock *MockFeatureFlagServiceInterface
}

// NewMockFeatureFlagServiceInterface creates a new mock instance.
func NewMockFeatureFlagServiceInterface(ctrl *gomock.Controller) *MockFeatureFlagServiceInterface {
	mock := &MockFeatureFlagServiceInterface{ctrl: ctrl}
	mock.recorder = &MockFeatureFlagServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeatureFlagServiceInterface) EXPECT() *MockFeatureFlagServiceInterfaceMockRecorder {
	return m.recorder
}

// GetBy mocks base method.
func (m *MockFeatureFlagServiceInterface) GetBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBy", ctx, platform, version)
	ret0, _ := ret[0].(*models.FeatureFlag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBy indicates an expected call of GetBy.
func (mr *MockFeatureFlagServiceInterfaceMockRecorder) GetBy(ctx, platform, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBy", reflect.TypeOf((*MockFeatureFlagServiceInterface)(nil).GetBy), ctx, platform, version)
}
