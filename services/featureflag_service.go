package services

import (
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"context"
	"errors"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// FeatureFlagServiceInterface defines the interface for feature flag service operations
type FeatureFlagServiceInterface interface {
	GetBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error)
}

type FeatureFlagService struct {
	repository repositories.FeatureFlagRepositoryInterface
	DB         *pgxpool.Pool
	logger     *zap.Logger
}

func NewFeatureFlagService(repository repositories.FeatureFlagRepositoryInterface, db *pgxpool.Pool, logger *zap.Logger) *FeatureFlagService {
	return &FeatureFlagService{
		repository: repository,
		DB:         db,
		logger:     logger,
	}
}

func (s *FeatureFlagService) GetBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error) {
	featureFlags, err := s.repository.GetBy(ctx, platform, version)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil
		}
		s.logger.Error("fetching feature flags by platform and version", zap.Any("platform", platform), zap.Any("version", version), zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return featureFlags, nil
}
