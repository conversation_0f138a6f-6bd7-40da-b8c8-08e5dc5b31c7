package services

import (
	"api.appio.so/internal/mocks"
	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"context"
	"encoding/json"
	"errors"
	"testing"

	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
)

// -------------------
// Tests for NotificationService
// -------------------

func TestNotificationService_FindByID(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	ntfID, _ := appioid.New("ntf_00000000000000000000000000")

	t.Run("Found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockNotificationRepositoryInterface(ctrl)
		// Only NotificationRepository is needed for this test.
		service := NewNotificationService(repo, nil, nil, &pgxpool.Pool{}, logger)
		expected := &models.NotificationResponseWithStats{
			ID: ntfID, // adjust fields as necessary
		}

		repo.EXPECT().
			FindByID(ctx, svcID, ntfID).
			Return(expected, nil)

		result, err := service.FindByID(ctx, svcID, ntfID)
		assert.NoError(t, err)
		assert.Equal(t, expected, result)
	})

	t.Run("Not Found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockNotificationRepositoryInterface(ctrl)
		service := NewNotificationService(repo, nil, nil, &pgxpool.Pool{}, logger)

		repo.EXPECT().
			FindByID(ctx, svcID, ntfID).
			Return(nil, pgx.ErrNoRows)

		result, err := service.FindByID(ctx, svcID, ntfID)
		assert.Nil(t, result)
		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("RawError", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockNotificationRepositoryInterface(ctrl)
		service := NewNotificationService(repo, nil, nil, &pgxpool.Pool{}, logger)

		repo.EXPECT().
			FindByID(ctx, svcID, ntfID).
			Return(nil, errors.New("db error"))

		result, err := service.FindByID(ctx, svcID, ntfID)
		assert.Nil(t, result)
		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestNotificationService_List(t *testing.T) {
	// TODO implement ths test
}

func TestNotificationService_ListWithStats(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID, _ := appioid.New("svc_00000000000000000000000000")

	t.Run("Success", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockNotificationRepositoryInterface(ctrl)
		service := NewNotificationService(repo, nil, nil, &pgxpool.Pool{}, logger)
		expectedList := []models.NotificationResponseWithStats{
			models.NotificationResponseWithStats{
				ID: nil, // Populate with real data as needed.
			},
		}
		repo.EXPECT().
			ListWithStats(ctx, svcID, gomock.Any()).
			Return(expectedList, nil)

		result, err := service.ListWithStats(ctx, svcID, "")
		assert.NoError(t, err)
		assert.Equal(t, expectedList, result)
	})

	t.Run("RawError", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repo := mocks.NewMockNotificationRepositoryInterface(ctrl)
		service := NewNotificationService(repo, nil, nil, &pgxpool.Pool{}, logger)

		repo.EXPECT().
			ListWithStats(ctx, svcID, gomock.Any()).
			Return(nil, errors.New("list error"))

		result, err := service.ListWithStats(ctx, svcID, "")
		assert.Nil(t, result)
		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func ntfCreateMocks(t *testing.T, logger *zap.Logger) (*mocks.MockNotificationRepositoryInterface, *mocks.MockNotificationDeliveryRepositoryInterface, *mocks.MockDeviceRepositoryInterface, *NotificationService, *gomock.Controller) {
	ctrl := gomock.NewController(t)

	// Mocks for all repositories used in Create.
	ntfRepo := mocks.NewMockNotificationRepositoryInterface(ctrl)
	ntfdlvRepo := mocks.NewMockNotificationDeliveryRepositoryInterface(ctrl)
	devRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)

	// Use a dummy *pgxpool.Pool (its methods are not used by our test).
	db := &pgxpool.Pool{}
	service := NewNotificationService(ntfRepo, ntfdlvRepo, devRepo, db, logger)

	// Override TxExecutor so that the transaction callback is executed immediately.
	service.TxExecutor = func(ctx context.Context, pool *pgxpool.Pool, logger *zap.Logger, fn func(tx pgx.Tx) error) error {
		return fn(nil) // Use nil transaction for simplicity
	}

	return ntfRepo, ntfdlvRepo, devRepo, service, ctrl
}

func TestNotificationService_Create(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID, _ := appioid.New("svc_00000000000000000000000000")
	//dvcID, _ := appioid.New("dvc_00000000000000000000000000")

	// Prepare a sample NotificationRequest.
	ntfReq := models.NotificationRequest{
		// Populate fields as required.
	}

	// Create dummy devices.
	//device1ID, _ := appioid.New("dvc_00000000000000000000000001")
	//device2ID, _ := appioid.New("dvc_00000000000000000000000002")
	//devices := []models.Device{
	//	{ID: device1ID},
	//	{ID: device2ID},
	//}

	t.Run("Success", func(t *testing.T) {
		ntfRepo, _, _, service, ctrl := ntfCreateMocks(t, logger)
		defer ctrl.Finish()

		// Expect notificationRepository.Create to be called
		ntfRepo.EXPECT().
			Create(ctx, notification_type.Foreground, gomock.Any(), svcID, ntfReq, notification_status.Created).
			Return(nil)

		ntfID, err := service.CreateForeground(ctx, svcID, nil, "", ntfReq)
		assert.NoError(t, err)
		assert.NotNil(t, ntfID)
	})

	//t.Run("Device list error", func(t *testing.T) {
	//	_, _, devRepo, service := ntfCreateMocks(logger)
	//
	//	devRepo.On("List", ctx, svcID).Return(nil, errors.New("list error"))
	//
	//	ntfID, err := service.Create(ctx, svcID, ntfReq)
	//	assert.Nil(t, ntfID)
	//	assert.Equal(t, pkg.ErrInternal, err)
	//	devRepo.AssertExpectations(t)
	//})

	//t.Run("Notification create error", func(t *testing.T) {
	//	ntfRepo, _, devRepo, service := ntfCreateMocks(logger)
	//
	//	devRepo.On("List", ctx, svcID).Return(devices, nil)
	//	// Simulate error on notification creation.
	//	ntfRepo.On("CreateTx", mock.Anything, ctx, mock.Anything, svcID, ntfReq, notification_status.Created).Return(errors.New("create error"))
	//
	//	ntfID, err := service.Create(ctx, svcID, ntfReq)
	//	assert.Nil(t, ntfID)
	//	assert.Equal(t, pkg.ErrInternal, err)
	//	devRepo.AssertExpectations(t)
	//	ntfRepo.AssertExpectations(t)
	//})
	//
	//t.Run("Notification delivery create error", func(t *testing.T) {
	//	ntfRepo, ntfdlvRepo, devRepo, service := ntfCreateMocks(logger)
	//
	//	devRepo.On("List", ctx, svcID).Return(devices, nil)
	//	// Notification creation succeeds.
	//	ntfRepo.On("CreateTx", mock.Anything, ctx, mock.Anything, svcID, ntfReq, notification_status.Created).Return(nil)
	//	// For the first device, delivery creation succeeds.
	//	ntfdlvRepo.On("CreateTx", mock.Anything, ctx, mock.Anything, mock.Anything, device1ID, notification_status.Created).Return(nil)
	//	// For the second device, simulate an error.
	//	ntfdlvRepo.On("CreateTx", mock.Anything, ctx, mock.Anything, mock.Anything, device2ID, notification_status.Created).Return(errors.New("delivery error"))
	//
	//	ntfID, err := service.Create(ctx, svcID, ntfReq)
	//	assert.Nil(t, ntfID)
	//	assert.Equal(t, pkg.ErrInternal, err)
	//	devRepo.AssertExpectations(t)
	//	ntfRepo.AssertExpectations(t)
	//	ntfdlvRepo.AssertExpectations(t)
	//})
}

func TestNotificationService_ListDeliveredByDevice(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockNotificationRepo := mocks.NewMockNotificationRepositoryInterface(ctrl)
	mockNotificationDeliveryRepo := mocks.NewMockNotificationDeliveryRepositoryInterface(ctrl)
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	logger := zap.NewNop()
	db := &pgxpool.Pool{}

	service := NewNotificationService(mockNotificationRepo, mockNotificationDeliveryRepo, mockDeviceRepo, db, logger)

	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success", func(t *testing.T) {
		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
		}

		mockNotificationDeliveryRepo.EXPECT().
			ListByDevice(gomock.Any(), svcID, dvcID, gomock.Any()).
			Return(expectedNotifications, nil)

		result, err := service.ListDeliveredByDevice(context.Background(), svcID, dvcID)

		assert.NoError(t, err)
		assert.Equal(t, expectedNotifications, result)
	})

	t.Run("RawError", func(t *testing.T) {
		mockNotificationDeliveryRepo.EXPECT().
			ListByDevice(gomock.Any(), svcID, dvcID, gomock.Any()).
			Return(nil, errors.New("database error"))

		result, err := service.ListDeliveredByDevice(context.Background(), svcID, dvcID)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestNotificationService_ListByStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockNotificationRepo := mocks.NewMockNotificationRepositoryInterface(ctrl)
	mockNotificationDeliveryRepo := mocks.NewMockNotificationDeliveryRepositoryInterface(ctrl)
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	logger := zap.NewNop()
	db := &pgxpool.Pool{}

	service := NewNotificationService(mockNotificationRepo, mockNotificationDeliveryRepo, mockDeviceRepo, db, logger)

	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success", func(t *testing.T) {
		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntf_00000000000000000000000001")},
		}

		mockNotificationRepo.EXPECT().
			List(gomock.Any(), svcID, gomock.Any()).
			Return(expectedNotifications, nil)

		result, err := service.List(context.Background(), svcID, "created")

		assert.NoError(t, err)
		assert.Equal(t, expectedNotifications, result)
	})

	t.Run("RawError", func(t *testing.T) {
		mockNotificationRepo.EXPECT().
			List(gomock.Any(), svcID, gomock.Any()).
			Return(nil, errors.New("database error"))

		result, err := service.List(context.Background(), svcID, "created")

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestNotificationService_ListAllByDevice(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockNotificationRepo := mocks.NewMockNotificationRepositoryInterface(ctrl)
	mockNotificationDeliveryRepo := mocks.NewMockNotificationDeliveryRepositoryInterface(ctrl)
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	logger := zap.NewNop()
	db := &pgxpool.Pool{}

	service := NewNotificationService(mockNotificationRepo, mockNotificationDeliveryRepo, mockDeviceRepo, db, logger)

	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success", func(t *testing.T) {
		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
		}

		mockNotificationDeliveryRepo.EXPECT().
			ListByDevice(gomock.Any(), svcID, dvcID, gomock.Any()).
			Return(expectedNotifications, nil)

		result, err := service.ListAllByDevice(context.Background(), svcID, dvcID, "queued")

		assert.NoError(t, err)
		assert.Equal(t, expectedNotifications, result)
	})

	t.Run("RawError", func(t *testing.T) {
		mockNotificationDeliveryRepo.EXPECT().
			ListByDevice(gomock.Any(), svcID, dvcID, gomock.Any()).
			Return(nil, errors.New("database error"))

		result, err := service.ListAllByDevice(context.Background(), svcID, dvcID, "queued")

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestNotificationService_ListAllByCustomerUserID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockNotificationRepo := mocks.NewMockNotificationRepositoryInterface(ctrl)
	mockNotificationDeliveryRepo := mocks.NewMockNotificationDeliveryRepositoryInterface(ctrl)
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	logger := zap.NewNop()
	db := &pgxpool.Pool{}

	service := NewNotificationService(mockNotificationRepo, mockNotificationDeliveryRepo, mockDeviceRepo, db, logger)

	svcID := appioid.MustParse("svc_00000000000000000000000001")
	customerUserID := "testUser123"

	t.Run("Success", func(t *testing.T) {
		expectedNotifications := []models.NotificationResponse{
			{ID: appioid.MustParse("ntfdlv_00000000000000000000000001")},
		}

		mockNotificationDeliveryRepo.EXPECT().
			ListByCustomerUserID(gomock.Any(), svcID, customerUserID, gomock.Any()).
			Return(expectedNotifications, nil)

		result, err := service.ListAllByCustomerUserID(context.Background(), svcID, customerUserID, "queued")

		assert.NoError(t, err)
		assert.Equal(t, expectedNotifications, result)
	})

	t.Run("RawError", func(t *testing.T) {
		mockNotificationDeliveryRepo.EXPECT().
			ListByCustomerUserID(gomock.Any(), svcID, customerUserID, gomock.Any()).
			Return(nil, errors.New("database error"))

		result, err := service.ListAllByCustomerUserID(context.Background(), svcID, customerUserID, "queued")

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestNotificationService_CreateForeground_ComprehensiveTests(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")
	customerUserID := "testUser123"

	ntfReq := models.NotificationRequest{
		Payload: json.RawMessage(`{"title": "Test Notification", "body": "Test Body"}`),
	}

	t.Run("Create for specific device", func(t *testing.T) {
		ntfRepo, ntfdlvRepo, devRepo, service, ctrl := ntfCreateMocks(t, logger)
		defer ctrl.Finish()

		// Mock device lookup
		device := &models.DeviceRecord{
			ID: dvcID,
		}
		devRepo.EXPECT().
			FindByID(ctx, svcID, dvcID).
			Return(device, nil)

		// Mock notification creation
		ntfRepo.EXPECT().
			CreateTx(gomock.Any(), ctx, notification_type.Foreground, gomock.Any(), svcID, ntfReq, notification_status.Queued).
			Return(nil)

		// Mock notification delivery creation
		ntfdlvRepo.EXPECT().
			CreateTx(gomock.Any(), ctx, gomock.Any(), gomock.Any(), dvcID, notification_status.Queued).
			Return(nil)

		ntfID, err := service.CreateForeground(ctx, svcID, dvcID, "", ntfReq)
		assert.NoError(t, err)
		assert.NotNil(t, ntfID)
	})

	t.Run("Create for customer user ID", func(t *testing.T) {
		ntfRepo, ntfdlvRepo, devRepo, service, ctrl := ntfCreateMocks(t, logger)
		defer ctrl.Finish()

		// Mock device lookup by customer user ID
		devices := []models.DeviceRecord{
			{ID: dvcID},
			{ID: appioid.MustParse("dvc_00000000000000000000000002")},
		}
		devRepo.EXPECT().
			FindByCustomerUserID(ctx, svcID, customerUserID).
			Return(devices, nil)

		// Mock notification creation
		ntfRepo.EXPECT().
			CreateTx(gomock.Any(), ctx, notification_type.Foreground, gomock.Any(), svcID, ntfReq, notification_status.Queued).
			Return(nil)

		// Mock notification delivery creation for each device
		ntfdlvRepo.EXPECT().
			CreateTx(gomock.Any(), ctx, gomock.Any(), gomock.Any(), devices[0].ID, notification_status.Queued).
			Return(nil)
		ntfdlvRepo.EXPECT().
			CreateTx(gomock.Any(), ctx, gomock.Any(), gomock.Any(), devices[1].ID, notification_status.Queued).
			Return(nil)

		ntfID, err := service.CreateForeground(ctx, svcID, nil, customerUserID, ntfReq)
		assert.NoError(t, err)
		assert.NotNil(t, ntfID)
	})

	t.Run("Device not found error", func(t *testing.T) {
		_, _, devRepo, service, ctrl := ntfCreateMocks(t, logger)
		defer ctrl.Finish()

		devRepo.EXPECT().
			FindByID(ctx, svcID, dvcID).
			Return(nil, pgx.ErrNoRows)

		ntfID, err := service.CreateForeground(ctx, svcID, dvcID, "", ntfReq)
		assert.Equal(t, pkg.ErrNotFound, err)
		assert.NotNil(t, ntfID) // ID is created before the error occurs
	})

	t.Run("No devices found for customer user ID", func(t *testing.T) {
		_, _, devRepo, service, ctrl := ntfCreateMocks(t, logger)
		defer ctrl.Finish()

		devRepo.EXPECT().
			FindByCustomerUserID(ctx, svcID, customerUserID).
			Return([]models.DeviceRecord{}, nil)

		ntfID, err := service.CreateForeground(ctx, svcID, nil, customerUserID, ntfReq)
		assert.Equal(t, pkg.ErrNotFound, err)
		assert.NotNil(t, ntfID) // ID is created before the error occurs
	})
}
