package services

import (
	"api.appio.so/models"
	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"context"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
	"testing"
)

func TestFeatureFlagService_GetBy(t *testing.T) {
	logger := zap.NewNop()
	var db *pgxpool.Pool
	ctx := context.Background()

	tests := []struct {
		name           string
		platform       models.Platform
		version        string
		mockResponse   *models.FeatureFlag
		mockError      error
		expectedResult *models.FeatureFlag
		expectedError  error
	}{
		{
			name:     "Success - iOS feature flag found",
			platform: models.PlatformIOS,
			version:  "v1.0.0",
			mockResponse: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000001"),
				Platform: "ios",
				Version:  "v1.0.0",
				Config:   "{\"feature1\": true, \"feature2\": false}",
			},
			mockError: nil,
			expectedResult: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000001"),
				Platform: "ios",
				Version:  "v1.0.0",
				Config:   "{\"feature1\": true, \"feature2\": false}",
			},
			expectedError: nil,
		},
		{
			name:     "Success - Android feature flag found",
			platform: models.PlatformAndroid,
			version:  "v1.0.0",
			mockResponse: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000002"),
				Platform: "android",
				Version:  "v1.0.0",
				Config:   "{\"androidFeature\": true}",
			},
			mockError: nil,
			expectedResult: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000002"),
				Platform: "android",
				Version:  "v1.0.0",
				Config:   "{\"androidFeature\": true}",
			},
			expectedError: nil,
		},
		{
			name:           "Not found - returns nil",
			platform:       models.PlatformIOS,
			version:        "v2.0.0",
			mockResponse:   nil,
			mockError:      pgx.ErrNoRows,
			expectedResult: nil,
			expectedError:  nil,
		},
		{
			name:           "Repository error - returns ErrInternal",
			platform:       models.PlatformIOS,
			version:        "v1.0.0",
			mockResponse:   nil,
			mockError:      errors.New("database connection failed"),
			expectedResult: nil,
			expectedError:  pkg.ErrInternal,
		},
		{
			name:     "Success - empty config",
			platform: models.PlatformIOS,
			version:  "v0.1.0",
			mockResponse: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000003"),
				Platform: "ios",
				Version:  "v0.1.0",
				Config:   "{}",
			},
			mockError: nil,
			expectedResult: &models.FeatureFlag{
				ID:       appioid.MustParse("ff_00000000000000000000000003"),
				Platform: "ios",
				Version:  "v0.1.0",
				Config:   "{}",
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
			service := NewFeatureFlagService(mockRepo, db, logger)

			mockRepo.EXPECT().
				GetBy(ctx, tt.platform, tt.version).
				Return(tt.mockResponse, tt.mockError)

			result, err := service.GetBy(ctx, tt.platform, tt.version)

			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func TestFeatureFlagService_GetBy_EdgeCases(t *testing.T) {
	logger := zap.NewNop()
	var db *pgxpool.Pool
	ctx := context.Background()

	t.Run("Empty version string", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformIOS, "").
			Return(nil, pgx.ErrNoRows)

		result, err := service.GetBy(ctx, models.PlatformIOS, "")
		assert.NoError(t, err)
		assert.Nil(t, result)
	})

	t.Run("Very long version string", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		longVersion := "v1.0.0-" + string(make([]byte, 1000)) // Very long version
		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformAndroid, longVersion).
			Return(nil, pgx.ErrNoRows)

		result, err := service.GetBy(ctx, models.PlatformAndroid, longVersion)
		assert.NoError(t, err)
		assert.Nil(t, result)
	})

	t.Run("Special characters in version", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		specialVersion := "v1.0.0-alpha+build.123!@#$%"
		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000004"),
			Platform: "ios",
			Version:  specialVersion,
			Config:   "{\"specialFeature\": true}",
		}

		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformIOS, specialVersion).
			Return(expectedFeatureFlag, nil)

		result, err := service.GetBy(ctx, models.PlatformIOS, specialVersion)
		assert.NoError(t, err)
		assert.Equal(t, expectedFeatureFlag, result)
	})

	t.Run("Unicode version string", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		unicodeVersion := "v1.0.0-测试版本"
		expectedFeatureFlag := &models.FeatureFlag{
			ID:       appioid.MustParse("ff_00000000000000000000000005"),
			Platform: "android",
			Version:  unicodeVersion,
			Config:   "{\"unicodeFeature\": true}",
		}

		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformAndroid, unicodeVersion).
			Return(expectedFeatureFlag, nil)

		result, err := service.GetBy(ctx, models.PlatformAndroid, unicodeVersion)
		assert.NoError(t, err)
		assert.Equal(t, expectedFeatureFlag, result)
	})
}

func TestFeatureFlagService_GetBy_ContextHandling(t *testing.T) {
	logger := zap.NewNop()
	var db *pgxpool.Pool

	t.Run("Context cancellation", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformIOS, "v1.0.0").
			Return(nil, context.Canceled)

		result, err := service.GetBy(ctx, models.PlatformIOS, "v1.0.0")
		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})

	t.Run("Context timeout", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		service := NewFeatureFlagService(mockRepo, db, logger)

		ctx := context.Background()
		mockRepo.EXPECT().
			GetBy(ctx, models.PlatformAndroid, "v1.0.0").
			Return(nil, context.DeadlineExceeded)

		result, err := service.GetBy(ctx, models.PlatformAndroid, "v1.0.0")
		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestFeatureFlagService_Constructor(t *testing.T) {
	t.Run("NewFeatureFlagService creates service correctly", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
		db := &pgxpool.Pool{}
		logger := zap.NewNop()

		service := NewFeatureFlagService(mockRepo, db, logger)

		assert.NotNil(t, service)
		assert.Equal(t, mockRepo, service.repository)
		assert.Equal(t, db, service.DB)
		assert.Equal(t, logger, service.logger)
	})

	t.Run("NewFeatureFlagService with nil parameters", func(t *testing.T) {
		service := NewFeatureFlagService(nil, nil, nil)

		assert.NotNil(t, service)
		assert.Nil(t, service.repository)
		assert.Nil(t, service.DB)
		assert.Nil(t, service.logger)
	})
}
